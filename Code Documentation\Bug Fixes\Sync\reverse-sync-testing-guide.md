# Reverse Sync Testing Guide

## Overview
This guide provides a systematic approach to test and debug reverse sync issues where items are not properly imported from backup directory to database.

## Test Setup

### 1. Initial State Preparation
```powershell
# 1. Start with clean database
# Delete the database file
Remove-Item "C:\Users\<USER>\AppData\Roaming\noti\noti-database.sqlite" -Force

# 2. Start the app to create fresh database
npm run dev
# Wait for app to fully load, then close it
```

### 2. Create Test Data
```powershell
# Create test structure in app:
# - Add a book: "Test Book"
# - Add a standalone folder at root: "Standalone Folder" 
# - Add a note in the book folder: "Book Note" with content "This is book note content"
# - Add a note in standalone folder: "Standalone Note" with content "This is standalone note content"
```

### 3. Initial Sync (Export)
```powershell
# 1. Set sync directory to: C:\temp\sync-test
# 2. Perform sync - this should export all items
# 3. Verify directory structure matches expected layout
```

## Data Collection Points

### Point A: After Initial Export
**Copy these files:**
```powershell
# 1. Copy sync manifest
Copy-Item "C:\temp\sync-test\sync-manifest.json" "C:\temp\debug\manifest-after-export.json"

# 2. Copy database for reference
Copy-Item "C:\Users\<USER>\AppData\Roaming\noti\noti-database.sqlite" "C:\temp\debug\database-after-export.sqlite"

# 3. Document directory structure
Get-ChildItem "C:\temp\sync-test" -Recurse | Out-File "C:\temp\debug\directory-after-export.txt"
```

**Console Logs to Capture:**
- Look for manifest generation logs
- Note any warnings about folder paths
- Record sync result summary

### Point B: Before Reverse Sync
```powershell
# 1. Delete database to simulate fresh install
Remove-Item "C:\Users\<USER>\AppData\Roaming\noti\noti-database.sqlite" -Force

# 2. Start app (creates fresh database with only default Books folder)
npm run dev

# 3. Set sync directory back to: C:\temp\sync-test
# 4. DO NOT SYNC YET - just configure the directory
```

**Copy these files:**
```powershell
# Copy fresh database state
Copy-Item "C:\Users\<USER>\AppData\Roaming\noti\noti-database.sqlite" "C:\temp\debug\database-fresh.sqlite"
```

### Point C: During Reverse Sync
**Console Logs to Monitor:**
```
[Manifest] Found existing manifest, validating...
[Manifest] Validated existing manifest with X items
[ImportFolder] Folder "..." has book relationship but no parent, setting parent to Books folder
[ImportNote] Note "..." has book relationship but invalid path "...", attempting to correct
```

**Key Questions:**
1. How many items does the manifest contain?
2. Are all expected items (book, folders, notes) present in manifest?
3. What items are detected for import vs export vs conflicts?

### Point D: After Reverse Sync
**Copy these files:**
```powershell
# Copy final database state
Copy-Item "C:\Users\<USER>\AppData\Roaming\noti\noti-database.sqlite" "C:\temp\debug\database-after-import.sqlite"

# Copy updated manifest
Copy-Item "C:\temp\sync-test\sync-manifest.json" "C:\temp\debug\manifest-after-import.json"
```

## Specific Tests to Perform

### Test 1: Manifest Content Analysis
```powershell
# After Point A, examine manifest-after-export.json
# Look for:
# 1. Book entry with type: "book"
# 2. Standalone folder entry with type: "folder" and no relationships.bookId
# 3. Book folder entry with type: "folder" and relationships.bookId
# 4. Notes with proper relationships.folderId or relationships.bookId
```

### Test 2: Change Detection Analysis
**Add debug logging to change-detector.ts:**
```typescript
// In compareStates method, add these logs:
console.log(`[ChangeDetector] Manifest items: ${manifest.items.length}`);
console.log(`[ChangeDetector] DB books: ${dbBooks.length}, folders: ${dbFolders.length}, notes: ${dbNotes.length}`);
console.log(`[ChangeDetector] Items to import - books: ${toImport.books.length}, folders: ${toImport.folders.length}, notes: ${toImport.notes.length}`);

// Log each item being evaluated:
for (const item of manifestItems) {
  const inDb = dbIds.has(item.id);
  const inDeletions = deletionSet.has(item.id);
  console.log(`[ChangeDetector] Item ${item.id} (${item.type}): inDb=${inDb}, inDeletions=${inDeletions}, willImport=${!inDb && !inDeletions}`);
}
```

### Test 3: File Content Verification
```powershell
# After Point A, check actual file contents:
Get-Content "C:\temp\sync-test\Books\Test Book\Book Note.md"
Get-Content "C:\temp\sync-test\Standalone Folder\Standalone Note.md"

# Verify files exist and have expected content
Test-Path "C:\temp\sync-test\Books\Test Book\.cover.jpg"
```

### Test 4: Database State Comparison
```sql
-- Compare database states using SQLite browser or command line

-- Fresh database (should only have Books folder):
SELECT 'FRESH DB' as state, 'folders' as table_name, id, name, parent_id, book_id FROM folders;
SELECT 'FRESH DB' as state, 'books' as table_name, id, title FROM books;
SELECT 'FRESH DB' as state, 'notes' as table_name, id, title, folder_id, book_id FROM notes;

-- After import database:
SELECT 'AFTER IMPORT' as state, 'folders' as table_name, id, name, parent_id, book_id FROM folders;
SELECT 'AFTER IMPORT' as state, 'books' as table_name, id, title FROM books;
SELECT 'AFTER IMPORT' as state, 'notes' as table_name, id, title, folder_id, book_id, length(content) as content_length FROM notes;
```

## Debug Information to Collect

### 1. Console Output Patterns
**Look for these specific log patterns:**
```
[UnifiedSyncEngine] Checking for renamed items...
[UnifiedSyncEngine] No renamed items detected
[ImportFolder] Folder "..." already exists with ID X, updating
[ImportNote] Note "..." already exists with ID X, updating
Detected folder rename: "..." -> "..."
```

### 2. Sync Result Summary
**Capture the final sync result:**
```
Sync history entry: {
  timestamp: '...',
  result: 'success',
  itemsImported: X,
  itemsExported: Y,
  conflicts: Z,
  errors: [],
  duration: N
}
```

### 3. Directory Structure Comparison
```powershell
# Before and after directory listings
Get-ChildItem "C:\temp\sync-test" -Recurse | Select-Object FullName, Length, LastWriteTime
```

## Expected vs Actual Results

### Expected After Reverse Sync:
- **Books**: 1 book ("Test Book") imported
- **Folders**: 2 folders (book folder + standalone folder) imported  
- **Notes**: 2 notes with full content imported
- **Cover**: Book cover image imported if it existed

### Common Failure Patterns:
1. **Standalone folder missing**: Check if it's in manifest but not imported
2. **Empty note content**: Check if file exists but content is empty
3. **Missing cover**: Check if .cover.jpg exists in book folder
4. **Wrong folder hierarchy**: Check if folders are created in wrong locations

## Files to Share for Analysis

After running the complete test, share these files:
1. `manifest-after-export.json`
2. `manifest-after-import.json` 
3. `directory-after-export.txt`
4. Complete console output from reverse sync operation
5. Database query results showing final state

## Temporary Debug Code Additions

### 1. Enhanced Change Detection Logging
**Add to `electron/main/api/sync-logic/change-detector.ts` in `compareStates` method:**
```typescript
// After line 64 (const manifestByType = this.categorizeByType(manifest.items);)
console.log(`[DEBUG] Manifest items by type:`, {
  books: manifestByType.books.map(b => ({ id: b.id, name: b.name, path: b.path })),
  folders: manifestByType.folders.map(f => ({ id: f.id, name: f.name, path: f.path, relationships: f.relationships })),
  notes: manifestByType.notes.map(n => ({ id: n.id, name: n.name, path: n.path, relationships: n.relationships }))
});

// After line 92 (const itemsToImport = this.findItemsToImport...)
console.log(`[DEBUG] Items to import for ${type}:`, itemsToImport.map(item => ({
  id: item.id,
  name: item.name,
  path: item.path,
  relationships: item.relationships
})));
```

### 2. Enhanced Import Logging
**Add to `unified-sync-engine.ts` in each import method:**

**In `importFolder` method (around line 866):**
```typescript
// At the start of the method
console.log(`[DEBUG] Importing folder:`, {
  id: item.id,
  name: item.name,
  path: item.path,
  relationships: item.relationships,
  metadata: item.metadata
});
```

**In `importNote` method (around line 972):**
```typescript
// After reading content (line 992)
console.log(`[DEBUG] Importing note:`, {
  id: item.id,
  name: item.name,
  path: item.path,
  notePath: notePath,
  contentLength: content.length,
  relationships: item.relationships,
  metadata: item.metadata
});
```

### 3. File Content Verification Script
**Create `debug-file-check.ps1`:**
```powershell
param(
    [string]$SyncDir = "C:\temp\sync-test"
)

Write-Host "=== FILE CONTENT VERIFICATION ===" -ForegroundColor Green

# Check manifest
$manifestPath = Join-Path $SyncDir "sync-manifest.json"
if (Test-Path $manifestPath) {
    $manifest = Get-Content $manifestPath | ConvertFrom-Json
    Write-Host "Manifest items: $($manifest.items.Count)" -ForegroundColor Yellow

    foreach ($item in $manifest.items) {
        Write-Host "  $($item.type): $($item.id) - $($item.name) - $($item.path)" -ForegroundColor Cyan

        if ($item.type -eq "note") {
            $notePath = Join-Path $SyncDir $item.path
            if (Test-Path $notePath) {
                $content = Get-Content $notePath -Raw
                Write-Host "    Content length: $($content.Length) chars" -ForegroundColor White
                if ($content.Length -eq 0) {
                    Write-Host "    WARNING: Empty note file!" -ForegroundColor Red
                }
            } else {
                Write-Host "    ERROR: Note file not found!" -ForegroundColor Red
            }
        }

        if ($item.type -eq "book") {
            $bookPath = Join-Path $SyncDir $item.path
            $coverPath = Join-Path $bookPath ".cover.jpg"
            if (Test-Path $coverPath) {
                $coverSize = (Get-Item $coverPath).Length
                Write-Host "    Cover image: $coverSize bytes" -ForegroundColor White
            } else {
                Write-Host "    No cover image found" -ForegroundColor Yellow
            }
        }
    }
} else {
    Write-Host "ERROR: Manifest not found!" -ForegroundColor Red
}
```

### 4. Database State Query Script
**Create `debug-db-check.sql`:**
```sql
-- Run this in SQLite browser or command line tool

.headers on
.mode table

SELECT 'BOOKS' as category, id, title as name, created_at, updated_at FROM books
UNION ALL
SELECT 'FOLDERS' as category, id, name, created_at, updated_at FROM folders
UNION ALL
SELECT 'NOTES' as category, id, title as name, created_at, updated_at FROM notes
ORDER BY category, id;

-- Detailed folder relationships
SELECT
    f.id,
    f.name,
    f.parent_id,
    f.book_id,
    b.title as book_name,
    pf.name as parent_folder_name
FROM folders f
LEFT JOIN books b ON f.book_id = b.id
LEFT JOIN folders pf ON f.parent_id = pf.id
ORDER BY f.book_id, f.parent_id, f.id;

-- Note relationships and content
SELECT
    n.id,
    n.title,
    n.folder_id,
    n.book_id,
    f.name as folder_name,
    b.title as book_name,
    LENGTH(n.content) as content_length,
    CASE WHEN LENGTH(n.content) = 0 THEN 'EMPTY' ELSE 'HAS_CONTENT' END as content_status
FROM notes n
LEFT JOIN folders f ON n.folder_id = f.id
LEFT JOIN books b ON n.book_id = b.id
ORDER BY n.book_id, n.folder_id, n.id;
```

## Step-by-Step Execution

### Phase 1: Setup and Export
1. Delete database, start app, create test data
2. Perform initial sync (export)
3. Run `debug-file-check.ps1` to verify exported files
4. Copy manifest and database files
5. **SHARE: manifest-after-export.json and console output**

### Phase 2: Prepare for Import
1. Delete database, restart app
2. Set sync directory (don't sync yet)
3. Run database query to confirm fresh state
4. **SHARE: Fresh database query results**

### Phase 3: Reverse Sync
1. Add debug logging code above
2. Perform sync with enhanced logging
3. Capture complete console output
4. Run `debug-file-check.ps1` again
5. Run database queries to check final state
6. **SHARE: Complete console output, final database state, manifest-after-import.json**

This systematic approach will help identify exactly where in the sync pipeline the issues are occurring.








okey so i added a backup, added books and folderss andnotes, and then i also deleted the db and imported that backup directory, and here are the logs and manifest at each stage: HERE IS THE INITIAL MANIFEST AND LOG AFTER HAVING ADDED THE FOLDERS AND THE BOOK AND NOTE: 
{
  "version": 1,
  "deviceId": "c8fac135-ce3a-42f2-8ef0-faea2a323d8f",
  "lastSync": "2025-06-18T07:06:53.519Z",
  "items": [
    {
      "id": "book_1",
      "type": "book",
      "name": "Wuthering Heights",
      "path": "Books/Wuthering Heights_2/",
      "hash": "faad5ca74f07c783af3f1d171eed6980f027cd0406bafe309195fc45a92ba3a7",
      "modified": "2025-06-18T07:05:56.896Z",
      "metadata": {
        "author": "Emily Brontë",
        "isbn": "0671453521",
        "publication_date": "1846",
        "description": "Wuthering Heights is an 1847 novel by Emily Brontë, initially published under the pseudonym Ellis Bell. It concerns two families of the landed gentry living on the West Yorkshire moors, the Earnshaws and the Lintons, and their turbulent relationships with Earnshaw's adopted son, Heathcliff. The novel was influenced by Romanticism and Gothic fiction.",
        "page_count": null,
        "current_page": null,
        "rating": null,
        "status": "unread",
        "olid": "OL21177W",
        "language": "gre",
        "genres": "British",
        "created_at": "2025-06-18T07:05:56.891Z",
        "updated_at": "2025-06-18T07:05:56.896Z"
      }
    },
    {
      "id": "folder_1",
      "type": "folder",
      "name": "Books",
      "path": "Books/",
      "hash": "3b7912dce0e4b60bae7cdf7d6f0caa02b085f1e2fe1070e44cfcf31cae9a482c",
      "modified": "2025-06-18 07:05:23"
    },
    {
      "id": "folder_3",
      "type": "folder",
      "name": "TestingStandAloneFolder",
      "path": "TestingStandAloneFolder/",
      "hash": "bd84093e2d2a1aefa0afb72b85c96ddb8aed21c15cddb5f40f7bbd00bce850e9",
      "modified": "2025-06-18T07:06:22.471Z"
    },
    {
      "id": "folder_2",
      "type": "folder",
      "name": "Wuthering Heights",
      "path": "Books/Wuthering Heights/",
      "hash": "e1f9b5f40ea0e3f0f4d9d34b1af8a2c5c160da45f3ae66f226b5bb830291e626",
      "modified": "2025-06-18T07:05:56.898Z",
      "relationships": {
        "bookId": "book_1",
        "parentId": "folder_1"
      }
    },
    {
      "id": "folder_4",
      "type": "folder",
      "name": "TestingSubFolder",
      "path": "TestingStandAloneFolder/TestingSubFolder/",
      "hash": "55178d58489b8e0671d99463fd95634244bde10a47f8737f9c00ed5744db4f4b",
      "modified": "2025-06-18T07:06:32.489Z",
      "relationships": {
        "parentId": "folder_3"
      }
    },
    {
      "id": "note_2",
      "type": "note",
      "name": "StandAloneNote",
      "path": "StandAloneNote.md",
      "hash": "22ad18dddf2bf7789e5f411860d86cff01976a4d3a3f05f06b3e0d8da4026f55",
      "modified": "2025-06-18T07:06:48.490Z",
      "metadata": {
        "type": "text",
        "last_viewed_at": "2025-06-18T07:06:39.727Z",
        "created_at": "2025-06-18T07:06:39.727Z",
        "updated_at": "2025-06-18T07:06:48.490Z"
      }
    },
    {
      "id": "note_1",
      "type": "note",
      "name": "Wuthering Heights - June 18, 2025",
      "path": "Books/Wuthering Heights/Wuthering Heights - June 18, 2025.md",
      "hash": "7d8314f352ed061111f7c910283c975705ab0201c1b96ee02bd5ec14e5fade23",
      "modified": "2025-06-18T07:06:07.632Z",
      "relationships": {
        "bookId": "book_1",
        "folderId": "folder_2"
      },
      "metadata": {
        "type": "text",
        "last_viewed_at": "2025-06-18T07:06:02.819Z",
        "created_at": "2025-06-18T07:06:02.819Z",
        "updated_at": "2025-06-18T07:06:07.632Z"
      }
    }
  ],
  "deletions": []
}


HERE IS THE TERMINAL LOG THAT RELATES TO THE ABOVE MANIFEST: 

PS C:\Users\<USER>\OneDrive - 365education\Desktop\Noti> npm run dev





PS C:\Users\<USER>\OneDrive - 365education\Desktop\Noti> npm run dev



PS C:\Users\<USER>\OneDrive - 365education\Desktop\Noti> npm run dev


PS C:\Users\<USER>\OneDrive - 365education\Desktop\Noti> npm run dev

PS C:\Users\<USER>\OneDrive - 365education\Desktop\Noti> npm run dev

PS C:\Users\<USER>\OneDrive - 365education\Desktop\Noti> npm run dev
PS C:\Users\<USER>\OneDrive - 365education\Desktop\Noti> npm run dev

> noti@0.8.0 dev
> vite

vite v6.3.5 building for development...

watching for file changes...
vite v6.3.5 building for development...

watching for file changes...

  VITE v6.3.5  ready in 343 ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
  ➜  press h + enter to show help

build started...

build started... (x2)
✓ 2 modules transformed.
dist-electron/preload/index.mjs  56.90 kB │ gzip: 14.54 kB │ map: 31.67 kB
built in 497ms.
✓ 24 modules transformed.
[plugin vite:reporter] 
(!) C:/Users/<USER>/OneDrive - 365education/Desktop/Noti/electron/main/api/notes-api.ts is dynamically imported by C:/Users/<USER>/OneDrive - 365education/Desktop/Noti/electron/main/api/folders-api.ts but also statically imported by C:/Users/<USER>/OneDrive - 365education/Desktop/Noti/electron/main/ipc-handlers.ts, dynamic import will not move module into another chunk.

[plugin vite:reporter]
(!) C:/Users/<USER>/OneDrive - 365education/Desktop/Noti/electron/main/api/sync-logic/unified-sync-engine.ts is dynamically imported by C:/Users/<USER>/OneDrive - 365education/Desktop/Noti/electron/main/api/sync-logic/manifest-manager.ts but also statically imported by C:/Users/<USER>/OneDrive - 365education/Desktop/Noti/electron/main/api/sync-logic/sync-api.ts, dynamic import will not move module into another chunk.

dist-electron/main/index.js  355.30 kB │ gzip: 66.00 kB │ map: 749.73 kB
built in 903ms.

≡ƒÄ« [DiscordRPC] Initializing Discord Rich Presence...
≡ƒÄ« [DiscordRPC] App start time set to: 2025-06-18T07:05:23.076Z
Media protocol handler registered successfully
Initializing database at: C:\Users\<USER>\AppData\Roaming\noti\noti-database.sqlite
Connected to the SQLite database.
WAL mode enabled successfully
Foreign key support enabled.
Default "Books" folder created successfully.
All database tables and indexes created successfully.
[DatabaseHooks] Initializing database hooks manager
Database hooks manager initialized.
Database singleton instance assigned.
Database initialized successfully
Sync API initialized successfully
Starting background cover download check...
Checking for books with missing cover files...
Database and IPC handlers initialized successfully
Checking for sync directory...
No books with missing covers found.
No sync directory configured or auto-sync disabled
Checking Discord Rich Presence settings...
No timer settings found, creating defaults.
[23712:0618/090524.385:ERROR:CONSOLE(1)] "Request Autofill.enable failed. {"code":-32601,"message":"'Autofill.enable' wasn't found"}", source: devtools://devtools/bundled/core/protocol_client/protocol_client.js (1)
[Manifest] Creating initial manifest with current database state
[ManifestManager] Root folder 1 (Books) -> Books/
[Manifest] Created initial manifest with 1 items from database
[Manifest] Performing initial sync to create physical files...
[ManifestManager] Root folder 1 (Books) -> Books/
[UnifiedSyncEngine] Checking for renamed items...
[UnifiedSyncEngine] No renamed items detected
[ManifestManager] Root folder 1 (Books) -> Books/
[UnifiedSyncEngine] Saved updated manifest with 1 items

=== SYNC DIRECTORY STRUCTURE ===
Root: C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti
--------------------------------------------------
+-- [DIR] Books/
+-- [JSON] sync-manifest.json

--------------------------------------------------
=== END SYNC DIRECTORY STRUCTURE ===

[Manifest] Initial sync completed successfully
[AutoSync] performSync called
[AutoSync] Emitting sync-start event
[SyncAPI] Received sync-start event from auto-sync
[ManifestManager] Root folder 1 (Books) -> Books/
[UnifiedSyncEngine] Checking for renamed items...
[UnifiedSyncEngine] No renamed items detected
[ManifestManager] Root folder 1 (Books) -> Books/
[UnifiedSyncEngine] Saved updated manifest with 1 items

=== SYNC DIRECTORY STRUCTURE ===
Root: C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti
--------------------------------------------------
+-- [DIR] Books/
+-- [JSON] sync-manifest.json

--------------------------------------------------
=== END SYNC DIRECTORY STRUCTURE ===

Sync history entry: {
  timestamp: '2025-06-18T07:05:38.153Z',
  result: 'success',
  itemsImported: 0,
  itemsExported: 1,
  conflicts: 1,
  errors: [],
  duration: 16
}
Enhanced search completed for "Testing": 10 results (10 with covers)
Attempting to download cover from: https://covers.openlibrary.org/b/id/12818862-L.jpg
Cover download redirected to: https://archive.org/download/l_covers_0012/l_covers_0012_81.zip/0012818862-L.jpg
Cover download redirected to: https://ia800100.us.archive.org/view_archive.php?archive=/5/items/l_covers_0012/l_covers_0012_81.zip&file=0012818862-L.jpg
Cover image downloaded and converted to base64.
[DatabaseHooks] Notifying auto-sync of change: book_create
[AutoSync] Database change detected: book_create
[AutoSync] Triggering debounced sync...
[DatabaseHooks] Change detected: create book (ID: 1)
Processing cover immediately for book "Wuthering Heights" (ID: 1)
Γ£ô Cover processed and saved immediately for book "Wuthering Heights"
Warning: Attempting to clear cover_url for book ID 1. This should be intentional.
Γ£ô Cleared base64 data URL from database for book "Wuthering Heights" to optimize sync

≡ƒôÜ BOOK FOLDER CREATION START
   ≡ƒôû Book: "Wuthering Heights" (ID: 1)
Books root folder found with ID: 1
   ≡ƒôü Books root folder: "Books" (ID: 1)
   ≡ƒÅ╖∩╕Å Sanitized folder name: "Wuthering Heights"
   ≡ƒöù Creating folder with:
     - name: "Wuthering Heights"
     - parent_id: 1
     - book_id: 1
[DatabaseHooks] Notifying auto-sync of change: folder_create
[AutoSync] Database change detected: folder_create
[AutoSync] Triggering debounced sync...
[DatabaseHooks] Change detected: create folder (ID: 2)
Γ£à Successfully created folder "Wuthering Heights" for book ID 1 under Books folder ID 1
≡ƒôÜ BOOK FOLDER CREATION END

Serving file: C:\Users\<USER>\AppData\Roaming\noti\media\1750230356894-cover.jpg
[AutoSync] performSync called
[AutoSync] Emitting sync-start event
[SyncAPI] Received sync-start event from auto-sync
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Folder 2 (Wuthering Heights) with parent 1 -> Books/Wuthering Heights/
[UnifiedSyncEngine] Checking for renamed items...
[UnifiedSyncEngine] No renamed items detected
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Folder 2 (Wuthering Heights) with parent 1 -> Books/Wuthering Heights/
[UnifiedSyncEngine] Saved updated manifest with 3 items

=== SYNC DIRECTORY STRUCTURE ===
Root: C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti
--------------------------------------------------
+-- [DIR] Books/
|   +-- [DIR] Wuthering Heights/
+-- [JSON] sync-manifest.json

--------------------------------------------------
=== END SYNC DIRECTORY STRUCTURE ===

Sync history entry: {
  timestamp: '2025-06-18T07:06:01.931Z',
  result: 'success',
  itemsImported: 0,
  itemsExported: 3,
  conflicts: 1,
  errors: [],
  duration: 21
}
Found folder for book ID 1 using book_id field: Wuthering Heights
[DatabaseHooks] Notifying auto-sync of change: note_create
[AutoSync] Database change detected: note_create
[AutoSync] Triggering debounced sync...
[DatabaseHooks] Change detected: create note (ID: 1)
Created note "Wuthering Heights - June 18, 2025" for book "Wuthering Heights" (ID: 1) in folder 2
[DatabaseHooks] Notifying auto-sync of change: note_update
[AutoSync] Database change detected: note_update
[AutoSync] Triggering debounced sync...
[DatabaseHooks] Change detected: update note (ID: 1)
IPC: Fetching folder hierarchy
Getting folder hierarchy with DB note counts...
Found 2 folders total with note counts.
Initialized folder map with 2 entries
Hierarchy built: 1 root folders, 1 child folders, 0 orphaned folders
Returning hierarchy with 1 root folders
IPC: Returning hierarchy with 1 root items
IPC: Fetching folder hierarchy
Getting folder hierarchy with DB note counts...
Found 3 folders total with note counts.
Initialized folder map with 3 entries
Hierarchy built: 2 root folders, 1 child folders, 0 orphaned folders
Returning hierarchy with 2 root folders
IPC: Returning hierarchy with 2 root items
[AutoSync] performSync called
[AutoSync] Emitting sync-start event
[SyncAPI] Received sync-start event from auto-sync
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Root folder 3 (New Folder) -> New Folder/
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Folder 2 (Wuthering Heights) with parent 1 -> Books/Wuthering Heights/
[UnifiedSyncEngine] Checking for renamed items...
[UnifiedSyncEngine] No renamed items detected
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Root folder 3 (New Folder) -> New Folder/
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Folder 2 (Wuthering Heights) with parent 1 -> Books/Wuthering Heights/
[UnifiedSyncEngine] Saved updated manifest with 5 items

=== SYNC DIRECTORY STRUCTURE ===
Root: C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti
--------------------------------------------------
+-- [DIR] Books/
|   +-- [DIR] Wuthering Heights/
|       +-- [MARKDOWN] Wuthering Heights - June 18, 2025.md
+-- [DIR] New Folder/
+-- [JSON] sync-manifest.json

--------------------------------------------------
=== END SYNC DIRECTORY STRUCTURE ===

Sync history entry: {
  timestamp: '2025-06-18T07:06:12.665Z',
  result: 'success',
  itemsImported: 0,
  itemsExported: 5,
  conflicts: 3,
  errors: [],
  duration: 18
}
[DatabaseHooks] Notifying auto-sync of change: folder_update
[AutoSync] Database change detected: folder_update
[AutoSync] Triggering debounced sync...
[DatabaseHooks] Change detected: update folder (ID: 3)
IPC: Fetching folder hierarchy
Getting folder hierarchy with DB note counts...
Found 3 folders total with note counts.
Initialized folder map with 3 entries
Hierarchy built: 2 root folders, 1 child folders, 0 orphaned folders
Returning hierarchy with 2 root folders
IPC: Returning hierarchy with 2 root items
IPC: Fetching folder hierarchy
Getting folder hierarchy with DB note counts...
Found 4 folders total with note counts.
Initialized folder map with 4 entries
Hierarchy built: 2 root folders, 2 child folders, 0 orphaned folders
Returning hierarchy with 2 root folders
IPC: Returning hierarchy with 2 root items
Request timeout
[AutoSync] performSync called
[AutoSync] Emitting sync-start event
[SyncAPI] Received sync-start event from auto-sync
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Root folder 3 (TestingStandAloneFolder) -> TestingStandAloneFolder/
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Folder 2 (Wuthering Heights) with parent 1 -> Books/Wuthering Heights/
[ManifestManager] Root folder 3 (TestingStandAloneFolder) -> TestingStandAloneFolder/
[ManifestManager] Folder 4 (New Folder) with parent 3 -> TestingStandAloneFolder/New Folder/
[UnifiedSyncEngine] Checking for renamed items...
[UnifiedSyncEngine] Detected rename: folder "New Folder" -> "TestingStandAloneFolder"
[UnifiedSyncEngine] Moving: C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti\New Folder\ -> C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti\TestingStandAloneFolder\   
[FileOperations] Successfully renamed directory: C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti\New Folder\ -> C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti\TestingStandAloneFolder\
[UnifiedSyncEngine] Successfully renamed folder: New Folder -> TestingStandAloneFolder
[UnifiedSyncEngine] Successfully processed 1 renamed items using fs.rename()
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Root folder 3 (TestingStandAloneFolder) -> TestingStandAloneFolder/
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Folder 2 (Wuthering Heights) with parent 1 -> Books/Wuthering Heights/
[ManifestManager] Root folder 3 (TestingStandAloneFolder) -> TestingStandAloneFolder/
[ManifestManager] Folder 4 (New Folder) with parent 3 -> TestingStandAloneFolder/New Folder/
[UnifiedSyncEngine] Saved updated manifest with 6 items

=== SYNC DIRECTORY STRUCTURE ===
Root: C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti
--------------------------------------------------
+-- [DIR] Books/
|   +-- [DIR] Wuthering Heights/
|       +-- [MARKDOWN] Wuthering Heights - June 18, 2025.md
+-- [DIR] TestingStandAloneFolder/
|   +-- [DIR] New Folder/
+-- [JSON] sync-manifest.json

--------------------------------------------------
=== END SYNC DIRECTORY STRUCTURE ===

Sync history entry: {
  timestamp: '2025-06-18T07:06:27.503Z',
  result: 'success',
  itemsImported: 0,
  itemsExported: 6,
  conflicts: 5,
  errors: [],
  duration: 21
}
[DatabaseHooks] Notifying auto-sync of change: folder_update
[AutoSync] Database change detected: folder_update
[AutoSync] Triggering debounced sync...
[DatabaseHooks] Change detected: update folder (ID: 4)
IPC: Fetching folder hierarchy
Getting folder hierarchy with DB note counts...
Found 4 folders total with note counts.
Initialized folder map with 4 entries
Hierarchy built: 2 root folders, 2 child folders, 0 orphaned folders
Returning hierarchy with 2 root folders
IPC: Returning hierarchy with 2 root items
[AutoSync] performSync called
[AutoSync] Emitting sync-start event
[SyncAPI] Received sync-start event from auto-sync
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Root folder 3 (TestingStandAloneFolder) -> TestingStandAloneFolder/
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Folder 2 (Wuthering Heights) with parent 1 -> Books/Wuthering Heights/
[ManifestManager] Root folder 3 (TestingStandAloneFolder) -> TestingStandAloneFolder/
[ManifestManager] Folder 4 (TestingSubFolder) with parent 3 -> TestingStandAloneFolder/TestingSubFolder/
[UnifiedSyncEngine] Checking for renamed items...
[UnifiedSyncEngine] Detected rename: folder "New Folder" -> "TestingSubFolder"
[UnifiedSyncEngine] Moving: C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti\TestingStandAloneFolder\New Folder\ -> C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti\TestingStandAloneFolder\TestingSubFolder\
[FileOperations] Successfully renamed directory: C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti\TestingStandAloneFolder\New Folder\ -> C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti\TestingStandAloneFolder\TestingSubFolder\
[UnifiedSyncEngine] Successfully renamed folder: New Folder -> TestingSubFolder
[UnifiedSyncEngine] Successfully processed 1 renamed items using fs.rename()
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Root folder 3 (TestingStandAloneFolder) -> TestingStandAloneFolder/
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Folder 2 (Wuthering Heights) with parent 1 -> Books/Wuthering Heights/
[ManifestManager] Root folder 3 (TestingStandAloneFolder) -> TestingStandAloneFolder/
[ManifestManager] Folder 4 (TestingSubFolder) with parent 3 -> TestingStandAloneFolder/TestingSubFolder/
[UnifiedSyncEngine] Saved updated manifest with 6 items

=== SYNC DIRECTORY STRUCTURE ===
Root: C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti
--------------------------------------------------
+-- [DIR] Books/
|   +-- [DIR] Wuthering Heights/
|       +-- [MARKDOWN] Wuthering Heights - June 18, 2025.md
+-- [DIR] TestingStandAloneFolder/
|   +-- [DIR] TestingSubFolder/
+-- [JSON] sync-manifest.json

--------------------------------------------------
=== END SYNC DIRECTORY STRUCTURE ===

Sync history entry: {
  timestamp: '2025-06-18T07:06:37.531Z',
  result: 'success',
  itemsImported: 0,
  itemsExported: 6,
  conflicts: 6,
  errors: [],
  duration: 27
}
[DatabaseHooks] Notifying auto-sync of change: note_create
[AutoSync] Database change detected: note_create
[AutoSync] Triggering debounced sync...
[DatabaseHooks] Change detected: create note (ID: 2)
[DatabaseHooks] Notifying auto-sync of change: note_update
[AutoSync] Database change detected: note_update
[AutoSync] Triggering debounced sync...
[DatabaseHooks] Change detected: update note (ID: 2)
[AutoSync] performSync called
[AutoSync] Emitting sync-start event
[SyncAPI] Received sync-start event from auto-sync
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Root folder 3 (TestingStandAloneFolder) -> TestingStandAloneFolder/
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Folder 2 (Wuthering Heights) with parent 1 -> Books/Wuthering Heights/
[ManifestManager] Root folder 3 (TestingStandAloneFolder) -> TestingStandAloneFolder/
[ManifestManager] Folder 4 (TestingSubFolder) with parent 3 -> TestingStandAloneFolder/TestingSubFolder/
[UnifiedSyncEngine] Checking for renamed items...
[UnifiedSyncEngine] No renamed items detected
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Root folder 3 (TestingStandAloneFolder) -> TestingStandAloneFolder/
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Folder 2 (Wuthering Heights) with parent 1 -> Books/Wuthering Heights/
[ManifestManager] Root folder 3 (TestingStandAloneFolder) -> TestingStandAloneFolder/
[ManifestManager] Folder 4 (TestingSubFolder) with parent 3 -> TestingStandAloneFolder/TestingSubFolder/
[UnifiedSyncEngine] Saved updated manifest with 7 items

=== SYNC DIRECTORY STRUCTURE ===
Root: C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti
--------------------------------------------------
+-- [DIR] Books/
|   +-- [DIR] Wuthering Heights/
|       +-- [MARKDOWN] Wuthering Heights - June 18, 2025.md
+-- [DIR] TestingStandAloneFolder/
|   +-- [DIR] TestingSubFolder/
+-- [JSON] sync-manifest.json
+-- [MARKDOWN] Untitled Note.md

--------------------------------------------------
=== END SYNC DIRECTORY STRUCTURE ===

Sync history entry: {
  timestamp: '2025-06-18T07:06:47.529Z',
  result: 'success',
  itemsImported: 0,
  itemsExported: 7,
  conflicts: 6,
  errors: [],
  duration: 25
}
[DatabaseHooks] Notifying auto-sync of change: note_update
[AutoSync] Database change detected: note_update
[AutoSync] Triggering debounced sync...
[DatabaseHooks] Change detected: update note (ID: 2)
[AutoSync] performSync called
[AutoSync] Emitting sync-start event
[SyncAPI] Received sync-start event from auto-sync
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Root folder 3 (TestingStandAloneFolder) -> TestingStandAloneFolder/
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Folder 2 (Wuthering Heights) with parent 1 -> Books/Wuthering Heights/
[ManifestManager] Root folder 3 (TestingStandAloneFolder) -> TestingStandAloneFolder/
[ManifestManager] Folder 4 (TestingSubFolder) with parent 3 -> TestingStandAloneFolder/TestingSubFolder/
[UnifiedSyncEngine] Checking for renamed items...
[UnifiedSyncEngine] Detected rename: note "Untitled Note" -> "StandAloneNote"
[UnifiedSyncEngine] Moving: C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti\Untitled Note.md -> C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti\StandAloneNote.md     
[FileOperations] Successfully renamed file: C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti\Untitled Note.md -> C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti\StandAloneNote.md
[UnifiedSyncEngine] Successfully renamed note: Untitled Note -> StandAloneNote
[UnifiedSyncEngine] Successfully processed 1 renamed items using fs.rename()
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Root folder 3 (TestingStandAloneFolder) -> TestingStandAloneFolder/
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Folder 2 (Wuthering Heights) with parent 1 -> Books/Wuthering Heights/
[ManifestManager] Root folder 3 (TestingStandAloneFolder) -> TestingStandAloneFolder/
[ManifestManager] Folder 4 (TestingSubFolder) with parent 3 -> TestingStandAloneFolder/TestingSubFolder/
[UnifiedSyncEngine] Saved updated manifest with 7 items

=== SYNC DIRECTORY STRUCTURE ===
Root: C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti
--------------------------------------------------
+-- [DIR] Books/
|   +-- [DIR] Wuthering Heights/
|       +-- [MARKDOWN] Wuthering Heights - June 18, 2025.md
+-- [DIR] TestingStandAloneFolder/
|   +-- [DIR] TestingSubFolder/
+-- [MARKDOWN] StandAloneNote.md
+-- [JSON] sync-manifest.json

--------------------------------------------------
=== END SYNC DIRECTORY STRUCTURE ===

Sync history entry: {
  timestamp: '2025-06-18T07:06:53.533Z',
  result: 'success',
  itemsImported: 0,
  itemsExported: 7,
  conflicts: 7,
  errors: [],
  duration: 27
}
  





HERE IS THE MANIFEST AND LOG AFTER HAVING DELETED THE DB AND STARTING OVER; THIS SIMULATES AN IMPORT OF A SYNC: 
{
  "version": 1,
  "deviceId": "82400090-1226-4764-aa8e-239cd2cd1011",
  "lastSync": "2025-06-18T07:09:51.062Z",
  "items": [
    {
      "id": "book_1",
      "type": "book",
      "name": "Wuthering Heights",
      "path": "Books/Wuthering Heights_2/",
      "hash": "faad5ca74f07c783af3f1d171eed6980f027cd0406bafe309195fc45a92ba3a7",
      "modified": "2025-06-18T07:09:51.037Z",
      "metadata": {
        "author": "Emily Brontë",
        "isbn": "0671453521",
        "publication_date": "1846",
        "description": "Wuthering Heights is an 1847 novel by Emily Brontë, initially published under the pseudonym Ellis Bell. It concerns two families of the landed gentry living on the West Yorkshire moors, the Earnshaws and the Lintons, and their turbulent relationships with Earnshaw's adopted son, Heathcliff. The novel was influenced by Romanticism and Gothic fiction.",
        "page_count": null,
        "current_page": null,
        "rating": null,
        "status": "unread",
        "olid": "OL21177W",
        "language": "gre",
        "genres": "British",
        "created_at": "2025-06-18T07:09:51.037Z",
        "updated_at": "2025-06-18T07:09:51.037Z"
      }
    },
    {
      "id": "folder_1",
      "type": "folder",
      "name": "Books",
      "path": "Books/",
      "hash": "3b7912dce0e4b60bae7cdf7d6f0caa02b085f1e2fe1070e44cfcf31cae9a482c",
      "modified": "2025-06-18 07:09:37"
    },
    {
      "id": "folder_2",
      "type": "folder",
      "name": "Wuthering Heights",
      "path": "Books/Wuthering Heights/",
      "hash": "e1f9b5f40ea0e3f0f4d9d34b1af8a2c5c160da45f3ae66f226b5bb830291e626",
      "modified": "2025-06-18T07:09:51.041Z",
      "relationships": {
        "bookId": "book_1",
        "parentId": "folder_1"
      }
    },
    {
      "id": "folder_3",
      "type": "folder",
      "name": "TestingSubFolder",
      "path": "Books/Wuthering Heights/TestingSubFolder/",
      "hash": "15563a2b6b5b6f77f30f07f80823c4bfce457280542b49ea3855df71fb8300d5",
      "modified": "2025-06-18T07:09:51.043Z",
      "relationships": {
        "parentId": "folder_2"
      }
    },
    {
      "id": "note_1",
      "type": "note",
      "name": "Wuthering Heights - June 18, 2025",
      "path": "Books/Wuthering Heights/Wuthering Heights - June 18, 2025.md",
      "hash": "7d8314f352ed061111f7c910283c975705ab0201c1b96ee02bd5ec14e5fade23",
      "modified": "2025-06-18T07:09:51.047Z",
      "relationships": {
        "bookId": "book_1",
        "folderId": "folder_2"
      },
      "metadata": {
        "type": "text",
        "last_viewed_at": "2025-06-18T07:06:02.819Z",
        "created_at": "2025-06-18T07:09:51.045Z",
        "updated_at": "2025-06-18T07:09:51.047Z"
      }
    }
  ],
  "deletions": []
}



PS C:\Users\<USER>\OneDrive - 365education\Desktop\Noti> npm run dev

> noti@0.8.0 dev
> vite

vite v6.3.5 building for development...

watching for file changes...
vite v6.3.5 building for development...

watching for file changes...

  VITE v6.3.5  ready in 333 ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
  ➜  press h + enter to show help

build started...

build started... (x2)
✓ 2 modules transformed.
dist-electron/preload/index.mjs  56.90 kB │ gzip: 14.54 kB │ map: 31.67 kB
built in 563ms.
✓ 24 modules transformed.
[plugin vite:reporter] 
(!) C:/Users/<USER>/OneDrive - 365education/Desktop/Noti/electron/main/api/notes-api.ts is dynamically imported by C:/Users/<USER>/OneDrive - 365education/Desktop/Noti/electron/main/api/folders-api.ts but also statically imported by C:/Users/<USER>/OneDrive - 365education/Desktop/Noti/electron/main/ipc-handlers.ts, dynamic import will not move module into another chunk.

[plugin vite:reporter]
(!) C:/Users/<USER>/OneDrive - 365education/Desktop/Noti/electron/main/api/sync-logic/unified-sync-engine.ts is dynamically imported by C:/Users/<USER>/OneDrive - 365education/Desktop/Noti/electron/main/api/sync-logic/manifest-manager.ts but also statically imported by C:/Users/<USER>/OneDrive - 365education/Desktop/Noti/electron/main/api/sync-logic/sync-api.ts, dynamic import will not move module into another chunk.

dist-electron/main/index.js  355.30 kB │ gzip: 66.00 kB │ map: 749.73 kB
built in 929ms.

≡ƒÄ« [DiscordRPC] Initializing Discord Rich Presence...
≡ƒÄ« [DiscordRPC] App start time set to: 2025-06-18T07:09:37.761Z
Media protocol handler registered successfully
Initializing database at: C:\Users\<USER>\AppData\Roaming\noti\noti-database.sqlite
Connected to the SQLite database.
WAL mode enabled successfully
Foreign key support enabled.
Default "Books" folder created successfully.
All database tables and indexes created successfully.
[DatabaseHooks] Initializing database hooks manager
Database hooks manager initialized.
Database singleton instance assigned.
Database initialized successfully
Sync API initialized successfully
Starting background cover download check...
Checking for books with missing cover files...
Database and IPC handlers initialized successfully
Checking for sync directory...
No books with missing covers found.
No sync directory configured or auto-sync disabled
Checking Discord Rich Presence settings...
No timer settings found, creating defaults.
[33956:0618/090938.906:ERROR:CONSOLE(1)] "Request Autofill.enable failed. {"code":-32601,"message":"'Autofill.enable' wasn't found"}", source: devtools://devtools/bundled/core/protocol_client/protocol_client.js (1)
[Manifest] Found existing manifest, validating...
[Manifest] Validated existing manifest with 7 items
[AutoSync] performSync called
[AutoSync] Emitting sync-start event
[SyncAPI] Received sync-start event from auto-sync
[ImportFolder] Folder "Wuthering Heights" has book relationship but no parent, setting parent to Books folder
[ImportFolder] Set parent to Books folder (ID: 1)
Folder "Wuthering Heights" already exists with ID 2, updating
Detected folder rename: "TestingStandAloneFolder" -> "Wuthering Heights"
Note "Wuthering Heights - June 18, 2025" already exists with ID 1, updating
Detected note rename: "StandAloneNote" -> "Wuthering Heights - June 18, 2025"
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Folder 2 (Wuthering Heights) with parent 1 -> Books/Wuthering Heights/
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Folder 2 (Wuthering Heights) with parent 1 -> Books/Wuthering Heights/
[ManifestManager] Folder 3 (TestingSubFolder) with parent 2 -> Books/Wuthering Heights/TestingSubFolder/
[UnifiedSyncEngine] Checking for renamed items...
[UnifiedSyncEngine] Detected rename: folder "TestingStandAloneFolder" -> "TestingSubFolder"
[UnifiedSyncEngine] Moving: C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti\TestingStandAloneFolder\ -> C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti\Books\Wuthering Heights\TestingSubFolder\
[FileOperations] Successfully renamed directory: C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti\TestingStandAloneFolder\ -> C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti\Books\Wuthering Heights\TestingSubFolder\
[UnifiedSyncEngine] Successfully renamed folder: TestingStandAloneFolder -> TestingSubFolder
[UnifiedSyncEngine] Successfully processed 1 renamed items using fs.rename()
Cleaning up renamed items: 1 folders, 0 books, 1 notes
Cleaning up renamed note: C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti\StandAloneNote.md
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Folder 2 (Wuthering Heights) with parent 1 -> Books/Wuthering Heights/
[ManifestManager] Root folder 1 (Books) -> Books/
[ManifestManager] Folder 2 (Wuthering Heights) with parent 1 -> Books/Wuthering Heights/
[ManifestManager] Folder 3 (TestingSubFolder) with parent 2 -> Books/Wuthering Heights/TestingSubFolder/
[UnifiedSyncEngine] Saved updated manifest with 5 items

=== SYNC DIRECTORY STRUCTURE ===
Root: C:\Users\<USER>\OneDrive - 365education\old stuff, scary disorganisation\Documents\TestingNoti
--------------------------------------------------
+-- [DIR] Books/
|   +-- [DIR] Wuthering Heights/
|       +-- [DIR] TestingSubFolder/
|       |   +-- [DIR] TestingSubFolder/
|       +-- [MARKDOWN] Wuthering Heights - June 18, 2025.md
+-- [JSON] sync-manifest.json

--------------------------------------------------
=== END SYNC DIRECTORY STRUCTURE ===

Sync history entry: {
  timestamp: '2025-06-18T07:09:51.075Z',
  result: 'success',
  itemsImported: 6,
  itemsExported: 1,
  conflicts: 1,
  errors: [],
  duration: 42
}
IPC: Fetching folder hierarchy
Getting folder hierarchy with DB note counts...
Found 3 folders total with note counts.
Initialized folder map with 3 entries
Hierarchy built: 1 root folders, 2 child folders, 0 orphaned folders
Returning hierarchy with 1 root folders
IPC: Returning hierarchy with 1 root items

